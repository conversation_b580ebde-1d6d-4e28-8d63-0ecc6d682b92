import {Component, Prop, Vue, Watch} from "vue-facing-decorator";
import {
	DropdownComponent,
	DropdownValue,
	FileInputComponent,
	FormModalOrDrawerComponent,
	InputPriceComponent, SelectMultipleDropdownComponent
} from "@groupk/vue3-interface-sdk";
import {
	ProductGroupData,
	ProductGroupItemData,
	ProductRevisionData,
	ProductRevisionPriceData, ProductRevisionPriceForItemData,
} from "../../../../../shared/mastodonCoreFront/product/ProductRevisionData";
import {
	ApplicationPermission,
	BillingAccountApiIn, BillingAccountCodeApiOut,
	BillingRegionApiOut,
	EstablishmentAccountPermissionModel,
	MetadataHttpCustomerDescriptorContract,
	MetadataModel,
	ProductApiOut, ProductBillingAccountHttpContract,
	ProductHttpProductContract,
	ProductModel,
	ProductType,
	UnitSystemApiOut,
	UuidScopeProductBillingRegion,
	UuidScopeProductProduct, UuidScopeProductProductRevision
} from "@groupk/mastodon-core";
import {AutoWired, ScopedUuid, SearchUtils, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeEstablishment} from "@groupk/mastodon-core";
import ProductImageComponent from "../ProductImageComponent/ProductImageComponent.vue";
import ProductMenuComponent from "../ProductComponents/ProductMenuComponent/ProductMenuComponent.vue";
import {ProductRepository} from "../../../../../shared/repositories/ProductRepository";
import {
	RecentlyUsedCompositionProductRepository
} from "../../../../../shared/repositories/RecentlyUsedCompositionProductRepository";
import {CleaveDirective} from "../../../../../shared/directives/CleaveDirective";
import UploadInputComponent from "../../../../../shared/components/UploadInputComponent/UploadInputComponent.vue";
import {MainConfig} from "../../../../../shared/MainConfig";
import {MetadataTargetSpecific, UuidScopeMetadata_descriptor} from "@groupk/mastodon-core";
import {MetadataDescriptorData} from "../../../../../shared/mastodonCoreFront/metadata/MetadataDescriptorData";
import {MetadataTargetApi} from "@groupk/mastodon-core";
import {BillingAccountApiOut} from "@groupk/mastodon-core";
import {BillingAccountRepository} from "../../../../../shared/repositories/BillingAccountRepository";
import {AppState} from "../../../../../shared/AppState";
import {ProductRevisionApiIn} from "@groupk/mastodon-core";
import {MetadataDescriptorRepository} from "../../../../../shared/repositories/MetadataDescriptorRepository";

export function ProductFormComponentComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]) {
	return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
		ProductBillingAccountHttpContract.set,
		ProductHttpProductContract.list,
		ProductHttpProductContract.create,
		ProductHttpProductContract.update,
		MetadataHttpCustomerDescriptorContract.listDescriptors,
		MetadataHttpCustomerDescriptorContract.updateDescriptor,
	]);
}

@Component({
	directives: {
		cleave: CleaveDirective,
	},
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'file-input-component': FileInputComponent,
		'dropdown': DropdownComponent,
		'select-multiple-dropdown': SelectMultipleDropdownComponent,
		'price-input': InputPriceComponent,
		'product-image': ProductImageComponent,
		'product-menu': ProductMenuComponent,
		'upload-input': UploadInputComponent
	},
	emits: ['created', 'updated', 'close', 'import-products-clicked']
})
export default class ProductFormComponent extends Vue {
	@Prop({required: true}) establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;
	@Prop({required: true}) billingRegions!: BillingRegionApiOut[];
	@Prop({required: true}) unitSystems!: UnitSystemApiOut[];
	@Prop({default: []}) billingAccounts!: BillingAccountApiOut[];
	@Prop({default: []}) billingAccountCodes!: BillingAccountCodeApiOut[];
	@Prop({default: null}) editingProduct!: ProductApiOut | null;
	@Prop({default: null}) defaultProductType!: ProductType | null;
	@Prop({default: true}) compositionEnabled!: boolean;

	selectedProductType: ProductType|null|undefined = undefined;

	product: ProductRevisionData = new ProductRevisionData();

	ProductRevisionApiInDefinition = ProductRevisionApiIn.__entityDefinition;

	products: ProductApiOut[] = [];
	metadataDescriptors: MetadataDescriptorData[] = [];
	displayedProducts: ProductApiOut[] = [];
	productSearch: string = '';
	selectedComposedProducts: VisualScopedUuid<UuidScopeProductProduct>[] = [];
	showCompositionModal: boolean = false;
	recentlyUsedCompositionProducts: VisualScopedUuid<UuidScopeProductProduct>[] = [];

	displayAdvanced: boolean = false;
	loadingProducts: boolean = false;
	opened: boolean = false;
	saving: boolean = false;
	error: string | null = null;
	errorConfiguration: "missing_region" | null = null;

	cleaveEan13: any = {
		numericOnly: true,
		blocks: [13]
	}

	ProductType = ProductType;

	@AutoWired(ProductRepository) private accessor productRepository!: ProductRepository;
	@AutoWired(MetadataDescriptorRepository) private accessor metadataRepository!: MetadataDescriptorRepository;
	@AutoWired(BillingAccountRepository) accessor billingAccountRepository!: BillingAccountRepository;
	@AutoWired(RecentlyUsedCompositionProductRepository) private accessor recentlyUsedCompositionProductRepository!: RecentlyUsedCompositionProductRepository;
	@AutoWired(MainConfig) private accessor mainConfig!: MainConfig;
	@AutoWired(AppState) private accessor appState!: AppState;

	@Watch('productSearch')
	productSearchWatch() {
		this.displayedProducts = SearchUtils.searchInTab(this.products, (product) => {
			return [product.lastRevision.name, product.lastRevision.ean13 ?? ''];
		}, this.productSearch).slice(-15).sort((productA, productB) => {
			if (this.recentlyUsedCompositionProducts.includes(productA.uid)) return -1;
			else if (this.recentlyUsedCompositionProducts.includes(productB.uid)) return 1;
			return 0;
		});
	}

	beforeMount() {
		this.loadingProducts = true;
		if(this.editingProduct) {
			this.product = new ProductRevisionData(this.editingProduct.lastRevision, this.editingProduct);
		} else if (this.billingRegions.length > 0) {
			this.setProductBillingRegion(this.billingRegions[0].uid)
		} else {
			this.errorConfiguration = "missing_region";
		}

		if(this.editingProduct) {
			this.selectedProductType = this.editingProduct.lastRevision.type;
		}

		if(!this.editingProduct && this.defaultProductType) {
			// Dans le cas où il n'y a pas de produit par défaut, on applique le type par défaut.
			this.selectedProductType = this.defaultProductType;
		}
	}

	async mounted() {
		setTimeout(() => this.opened = true, 0);

		const editingProductAccount = this.getBillingAccountForProduct();
		if(editingProductAccount) {
			const billingAccountCode = this.billingAccountCodes.find((code) => code.uid === editingProductAccount.billingAccountCodeUid);
			if(billingAccountCode) this.product.billingAccountCodeUid = billingAccountCode.uid;
		}

		this.recentlyUsedCompositionProducts = this.recentlyUsedCompositionProductRepository.get();

		this.products = (await this.productRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.displayedProducts = this.products.slice(-15).sort((productA, productB) => {
			if (this.recentlyUsedCompositionProducts.includes(productA.uid)) return -1;
			else if (this.recentlyUsedCompositionProducts.includes(productB.uid)) return 1;
			return 0;
		});

		this.metadataDescriptors = (await this.metadataRepository.callContract(
			'listDescriptors',
			{establishmentUid: this.establishmentUid},
			undefined)
		).success().map((descriptor) => new MetadataDescriptorData(descriptor));

		this.loadingProducts = false;
	}

	close() {
		this.opened = false;
		setTimeout(() => this.$emit('close'), 300);
	}

	importProducts() {
		this.$emit('import-products-clicked');
	}

	getMetadataDropdownValues(): DropdownValue[] {
		return this.metadataDescriptors.map((descriptor) => {
			return {
				value: descriptor.uid,
				name: descriptor.descriptorTemplate.name
			}
		});
	}

	getBillingAccountDropdownValues(): DropdownValue[] {
		return this.billingAccountCodes.map((billingAccountCode) => {
			return {
				name: billingAccountCode.name,
				value: billingAccountCode.uid
			}
		})
	}

	getMetadataDescriptorsForProduct(): MetadataDescriptorData[] {
		if(!this.editingProduct) return []
		return MetadataModel.applicableDescriptorOnTarget(this.metadataDescriptors, [
			{uid: MetadataTargetSpecific.ORDER_PURCHASE_ITEM, isChildren: true}
		]);
	}

	getBillingAccountForProduct(): BillingAccountApiOut|null {
		if(!this.editingProduct) return null;
		return this.billingAccounts.find((account) => account.targetUid === UuidUtils.visualToScoped<UuidScopeProductProductRevision>(this.editingProduct!.lastRevision.uid)) ?? null;
	}

	updateProductMetadataDescriptors(descriptorUids: VisualScopedUuid<UuidScopeMetadata_descriptor>[]) {
		if(!this.editingProduct) return;
		for(const descriptorUid of descriptorUids) {
			const descriptor = this.metadataDescriptors.find((descriptor) => descriptor.uid === descriptorUid);
			if(!descriptor) throw new Error('missing_descriptor');
			if(!this.doesDescriptorIncludeProduct(descriptor, this.editingProduct)) descriptor.targetList.push(new MetadataTargetApi({
				uid: UuidUtils.visualToScoped<UuidScopeProductProduct>(this.editingProduct.uid),
				children: true
			}));
		}

		for(const descriptor of this.metadataDescriptors) {
			if(this.doesDescriptorIncludeProduct(descriptor, this.editingProduct) && !descriptorUids.includes(descriptor.uid)) {
				const index = descriptor.targetList.map((target) => target.uid).findIndex((targetUid) => targetUid === UuidUtils.visualToScoped<UuidScopeProductProduct>(this.editingProduct!.uid));
				if(index !== -1) descriptor.targetList.splice(index, 1);
			}
		}
	}

	doesDescriptorIncludeProduct(metadataDescriptor: MetadataDescriptorData, product: ProductApiOut) {
		return metadataDescriptor.targetList.map((target) => target.uid).includes(UuidUtils.visualToScoped<UuidScopeProductProduct>(product.uid));
	}

	getBillingCategoryDropdownValues(): DropdownValue[] {
		return this.billingRegions.map((billingRegion) => {
			return {
				value: billingRegion.uid,
				name: billingRegion.taxes.map((tax) => tax.name + ' ' + (tax.percent / 1000) + '%').join(', ')
			}
		});
	}

	getUnitSystemsDropdownValues(): DropdownValue[] {
		return this.unitSystems.map((unitSystem) => {
			return {
				name: unitSystem.units.map((unit) => unit.name).join(', '),
				value: unitSystem.uid
			}
		});
	}

	getProductTypeDropdownValues(): DropdownValue[] {
		return [{
			name: 'Classique',
			value: 'null',
		}, {
			name: 'Menu',
			value: ProductType.FOOD_MENU,
		}]
	}

	getProductBillingRegion(): BillingRegionApiOut {
		if (this.product.prices.length === 0) {
			throw Error("product_has_no_price");
		}
		const billingRegionUid = this.product.prices[0].billingCategoryRegionUid;
		const billingRegion = this.billingRegions.find((billingRegion) => billingRegion.uid === billingRegionUid);
		if (!billingRegion) throw new Error('missing_region');
		return billingRegion;
	}

	setProductBillingRegion(billingRegionUid: VisualScopedUuid<UuidScopeProductBillingRegion>) {
		const priceData = new ProductRevisionPriceData();
		priceData.billingCategoryRegionUid = billingRegionUid;
		this.product.prices = [priceData];
	}


	setWithoutTaxes(price: ProductRevisionPriceData, value: number) {
		if (!price.billingCategoryRegionUid) return;

		price.withoutTaxes = value;

		const productModel = new ProductModel();
		const region = this.requireBillingRegion(price.billingCategoryRegionUid);
		if(!productModel.checkPriceWithAndWithoutTaxesAreWithinRange(price.withoutTaxes, price.withTaxes, region.taxes)) {
			price.withTaxes = productModel.computePriceWithTax(price.withoutTaxes, region.taxes);
		}
	}

	setWithTaxes(price: ProductRevisionPriceData, value: number) {
		if (!price.billingCategoryRegionUid) return;

		price.withTaxes = value;

		const productModel = new ProductModel();
		const region = this.requireBillingRegion(price.billingCategoryRegionUid);
		if(!productModel.checkPriceWithAndWithoutTaxesAreWithinRange(price.withoutTaxes, price.withTaxes, region.taxes)) {
			price.withoutTaxes = productModel.computePriceWithoutTaxAndTaxesWithPriceWithTax(price.withTaxes, region.taxes).priceWithoutTaxes;
		}
	}

	requireBillingRegion(regionUid: VisualScopedUuid<UuidScopeProductBillingRegion>) {
		const region = this.billingRegions.find((region) => region.uid === regionUid);
		if (!region) throw new Error('missing_region');
		return region;
	}

	async save() {
		if(this.selectedProductType === undefined) return;

		this.saving = true;
		this.error = null;

		try {
			this.product.type = this.selectedProductType;

			if(this.product.groups.length > 0) {
				this.product.uniquable = true;
			}

			if (!this.editingProduct) {
				if(this.product.type === ProductType.FOOD_MENU) {
					this.product.tangible = false;
					this.product.uniquable = true;
				}

				if(this.product.type === null && this.product.groups.length > 0) {
					this.product.tangible = true;
					this.product.uniquable = true;
				}

				const apiIn = this.product.toApiIn();

				const response = await this.productRepository.callContract('create', {establishmentUid: this.establishmentUid}, apiIn);
				if (response.isSuccess()) {
					const product = response.success();
					this.$emit('created', product);
				} else {
					const error = response.error();
					if (error && 'error' in error) this.error = error.error;
				}
			} else {
				const apiIn = this.product.toApiIn();

				const response = await this.productRepository.callContract('update', {establishmentUid: this.establishmentUid, productUid: this.editingProduct.uid}, apiIn);
				if (response.isSuccess()) {
					const product = response.success();
					this.$emit('updated', product);
				} else {
					const error = response.error();
					if (error && 'error' in error) this.error = error.error;
				}

				for(let descriptor of this.metadataDescriptors) {
					await this.metadataRepository.callContract('updateDescriptor', {establishmentUid: this.establishmentUid, customerAttributeDescriptorUid: descriptor.uid}, descriptor.toApi());
				}
			}
		} catch (err) {
			console.log(err);
		}

		this.saving = false;
	}

	toggleCompositionProduct(product: ProductApiOut) {
		const index = this.selectedComposedProducts.findIndex((productUid) => productUid === product.uid);
		if (index === -1) {
			this.selectedComposedProducts.push(product.uid);
		} else {
			this.selectedComposedProducts.splice(index, 1);
		}
	}

	addComposedProductsToParentProduct() {
		let groupData = this.product.groups.find((group) => group.name === 'ingredients');
		if (!groupData) {
			groupData = new ProductGroupData();
			groupData.name = 'ingredients';
			this.product.groups.push(groupData);
		}

		const groupItems: ProductGroupItemData[] = [];
		for (let productUid of this.selectedComposedProducts) {
			const existingItemWithProduct = groupData.items.find((item) => item.productUid === productUid);

			const product = this.requireProductWithUid(productUid);
			const productBillingRegion = this.requireBillingRegion(product.lastRevision.prices[0].billingCategoryRegionUid);

			const itemPrice = new ProductRevisionPriceForItemData();
			itemPrice.toCountry = productBillingRegion.toCountry;

			const item = new ProductGroupItemData();
			item.productUid = productUid;
			item.prices = [itemPrice];
			item.minQuantity = existingItemWithProduct ? existingItemWithProduct.minQuantity : 1;
			item.maxQuantity = existingItemWithProduct ? existingItemWithProduct.maxQuantity : 1;
			item.defaultQuantity = existingItemWithProduct ? existingItemWithProduct.defaultQuantity : 1;
			groupItems.push(item);
		}

		groupData.items = groupItems;

		this.recentlyUsedCompositionProducts = this.recentlyUsedCompositionProducts.concat(this.selectedComposedProducts).slice(-4);
		this.recentlyUsedCompositionProductRepository.set(this.recentlyUsedCompositionProducts);
		this.product.uniquable = true;
		this.showCompositionModal = false;
	}

	openCompositionModal() {
		const ingredientsGroup = this.ingredientsGroup;
		if (ingredientsGroup) {
			this.selectedComposedProducts = ingredientsGroup.items.map((item) => item.productUid).filter((productUid) => productUid !== null) as VisualScopedUuid<UuidScopeProductProduct>[];
		}
		this.showCompositionModal = true;
	}

	get ingredientsGroup() {
		return this.product.groups.find((group) => group.name === 'ingredients') ?? null;
	}

	requireProductWithUid(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		const product = this.products.find((product) => product.uid === productUid);
		if (!product) throw new Error('missing_product');
		return product;
	}

	getProductImageUrl(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		return this.mainConfig.configuration.mastodonApiEndpoint + `/Public/Product/v1/Establishments/${this.establishmentUid}/Products/${productUid}/Images/Main/contain/128/2024-01-18T20:30:20Z/b=0/image.png`
	}

	updateGroupItem(groupItem: ProductGroupItemData, event: Event) {
		if (!event.target) return;
		groupItem.minQuantity = parseInt((<HTMLInputElement>event.target).value, 10);
		groupItem.maxQuantity = parseInt((<HTMLInputElement>event.target).value, 10);
	}

	updateProductType(type: ProductType|'null') {
		const previousType = this.product.type;
		if(type === 'null') this.product.type = null;
		else this.product.type = type;

		if(this.product.type !== previousType) this.product.groups = [];
	}
}