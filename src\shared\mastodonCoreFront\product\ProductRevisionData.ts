import {CountryEnum, ScopedUuid, VisualScopedUuid} from "@groupk/horizon2-core";
import {
	ProductPriceApi,
	UuidScopeProductProductRevision,
	ProductRevisionApiIn,
	ProductRevisionApiOut,
	ProductGroupItemApiIn,
	UuidScopeProductProduct,
	ProductGroupApiIn,
	UuidScopeProductQuantityUnit,
	ProductType,
	ProductPriceForItemApi,
	ProductApiOut,
	UuidScopeProduct_stockSimple,
	UuidScopeProduct_stockTemporal
} from "@groupk/mastodon-core";
import {UuidScopeProductBillingRegion} from "@groupk/mastodon-core";
import {
	ProductGroupApiOut, ProductGroupItemApiOut,
	UuidScopeProductProductGroup,
	UuidScopeProductProductGroupItem
} from "@groupk/mastodon-core";
import {ProductRevisionTemplateTicketApi} from "@groupk/mastodon-core";
import {UuidScopeMastodon_upload} from "@groupk/mastodon-core";
import {UuidScopeProduct_billingAccountCode} from "@groupk/mastodon-core";

export class ProductDataError {
	error: string;
	message: string;
	constructor(error: string, message: string) {
		this.error = error;
		this.message = message;
	}
}

export class ProductRevisionPriceForItemData {
	toCountry: CountryEnum|null = null;
	withoutTaxes: number = 0;
	withTaxes: number = 0;

	constructor(productPrice: ProductPriceForItemApi|null = null) {
		if(productPrice) {
			this.toCountry = productPrice.toCountry;
			this.withoutTaxes = productPrice.withoutTaxes;
			this.withTaxes = productPrice.withTaxes;
		}
	}

	clone() {
		const clone = new ProductRevisionPriceForItemData();
		clone.toCountry = this.toCountry;
		clone.withoutTaxes = this.withoutTaxes;
		clone.withTaxes = this.withTaxes;
		return clone;
	}

	toApi() {
		if(!this.toCountry) throw new Error('missing_to_country');
		return new ProductPriceForItemApi({
			toCountry: this.toCountry,
			withoutTaxes: this.withoutTaxes,
			withTaxes: this.withTaxes,
		});
	}
}

export class ProductRevisionPriceData {
	billingCategoryRegionUid: VisualScopedUuid<UuidScopeProductBillingRegion>|null = null;
	withoutTaxes: number = 0;
	withTaxes: number = 0;

	constructor(productPrice: ProductPriceApi|null = null) {
		if(productPrice) {
			this.billingCategoryRegionUid = productPrice.billingCategoryRegionUid;
			this.withoutTaxes = productPrice.withoutTaxes;
			this.withTaxes = productPrice.withTaxes;
		}
	}

	clone() {
		const clone = new ProductRevisionPriceData();
		clone.billingCategoryRegionUid = this.billingCategoryRegionUid;
		clone.withoutTaxes = this.withoutTaxes;
		clone.withTaxes = this.withTaxes;
		return clone;
	}

	toApi() {
		if(!this.billingCategoryRegionUid) throw new Error('missing_billing_category_region');
		return new ProductPriceApi({
			billingCategoryRegionUid: this.billingCategoryRegionUid,
			withoutTaxes: this.withoutTaxes,
			withTaxes: this.withTaxes,
		});
	}
}

export class ProductGroupItemData {
	uid : VisualScopedUuid<UuidScopeProductProductGroupItem>|null = null;
	prices: ProductRevisionPriceForItemData[]|null = null;
	productUid: VisualScopedUuid<UuidScopeProductProduct>|null = null;
	minQuantity: number = 0;
	maxQuantity: number|string|null = null;
	defaultQuantity: number|string|null = null;

	constructor(productGroupItemApiOut: ProductGroupItemApiOut|null = null) {
		if(productGroupItemApiOut) {
			this.uid = productGroupItemApiOut.uid;
			this.prices = productGroupItemApiOut.prices ? productGroupItemApiOut.prices.map((price) => new ProductRevisionPriceForItemData(price)) : null;
			this.productUid = productGroupItemApiOut.product.uid;
			this.minQuantity = productGroupItemApiOut.minQuantity;
			this.maxQuantity = productGroupItemApiOut.maxQuantity;
			this.defaultQuantity = productGroupItemApiOut.defaultQuantity;
		}
	}

	clone() {
		const clone = new ProductGroupItemData();
		clone.uid = this.uid;
		clone.prices = this.prices ? this.prices.map((price) => price.clone()) : null;
		clone.productUid = this.productUid;
		clone.minQuantity = this.minQuantity;
		clone.maxQuantity = this.maxQuantity;
		clone.defaultQuantity = this.defaultQuantity;
		return clone;
	}

	toApiIn() {
		if(!this.productUid) throw new ProductDataError('invalid_ean13', 'L\'item n\'est relié à aucun produit');

		const maxQuantity = typeof this.maxQuantity === 'string' ? this.maxQuantity === '' ? null : Number(this.maxQuantity) : this.maxQuantity;
		const defaultQuantity = typeof this.defaultQuantity === 'string' ? this.defaultQuantity === '' ? null : Number(this.defaultQuantity) : this.defaultQuantity;

		return new ProductGroupItemApiIn({
			uid: this.uid,
			prices: this.prices ? this.prices.map((price) => price.toApi()) : null,
			productUid: this.productUid,
			minQuantity: this.minQuantity,
			maxQuantity: maxQuantity,
			defaultQuantity: defaultQuantity
		})
	}
}

export class ProductGroupData {
	uid : VisualScopedUuid<UuidScopeProductProductGroup>|null = null;
	name : string = '';
	items: ProductGroupItemData[] = [];
	minQuantity: number = 0;
	maxQuantity: number|null = null;

	constructor(productGroupApiOut: ProductGroupApiOut|null = null) {
		if(productGroupApiOut) {
			this.uid = productGroupApiOut.uid;
			this.name = productGroupApiOut.name;
			this.items = productGroupApiOut.items.map((item) => new ProductGroupItemData(item));
			this.minQuantity = productGroupApiOut.minQuantity;
			this.maxQuantity = productGroupApiOut.maxQuantity;
		}
	}

	clone() {
		const clone = new ProductGroupData();
		clone.uid = this.uid;
		clone.name = this.name;
		clone.items = this.items.map((item) => item.clone());
		clone.minQuantity = this.minQuantity;
		clone.maxQuantity = this.maxQuantity;
		return clone;
	}

	toApiIn() {
		return new ProductGroupApiIn({
			uid: this.uid,
			name: this.name,
			minQuantity: this.minQuantity,
			maxQuantity: this.maxQuantity,
			items: this.items.map((item) => item.toApiIn())
		});
	}
}

export class ProductRevisionData {
	uid: VisualScopedUuid<UuidScopeProductProductRevision>|null = null;
	name: string = '';
	description: string = '';
	ean13: string = '';
	uniquable: boolean = false;
	prices: ProductRevisionPriceData[] = [];
	mainImageUploadUid : VisualScopedUuid<UuidScopeMastodon_upload>|null = null;
	groups: ProductGroupData[] = [];
	stockUid: VisualScopedUuid<UuidScopeProduct_stockSimple>|VisualScopedUuid<UuidScopeProduct_stockTemporal>|null = null;
	unitUid: VisualScopedUuid<UuidScopeProductQuantityUnit>|null = null;
	type: ProductType|null = null;
	tangible: boolean = true;
	templateTickets: ProductRevisionTemplateTicketApi[] = [];
	onSaleEcommerceStartDatetime: string|undefined;
	onSaleEcommerceEndDatetime: string|undefined;
	billingAccountCodeUid: ScopedUuid<UuidScopeProduct_billingAccountCode>|undefined;

	constructor(productRevisionApiOut: ProductRevisionApiOut|null = null, productApiOut: ProductApiOut|null = null) {
		if(productRevisionApiOut) {
			this.uid = productRevisionApiOut.uid;
			this.name = productRevisionApiOut.name;
			this.ean13 = productRevisionApiOut.ean13 ?? '';
			this.uniquable = productRevisionApiOut.uniquable;
			this.groups = productRevisionApiOut.groups.map((group) => new ProductGroupData(group));
			this.prices = productRevisionApiOut.prices.map((price) => new ProductRevisionPriceData(price));
			this.stockUid = productRevisionApiOut.stockUid;
			this.unitUid = productRevisionApiOut.unitUid;
			this.type = productRevisionApiOut.type;
			this.tangible = productRevisionApiOut.tangible;
			this.templateTickets = productRevisionApiOut.templateTickets;
		}
		if(productApiOut) {
			this.description = productApiOut.description ?? '';
			this.onSaleEcommerceStartDatetime = productApiOut.onSaleEcommerceStartDatetime;
			this.onSaleEcommerceEndDatetime = productApiOut.onSaleEcommerceEndDatetime;
			this.mainImageUploadUid = productApiOut.mainImageUploadUid;
		}
	}

	clone() {
		const clone = new ProductRevisionData();
		clone.uid = this.uid;
		clone.name = this.name;
		clone.description = this.description;
		clone.ean13 = this.ean13;
		clone.uniquable = this.uniquable;
		clone.groups = this.groups.map((group) => group.clone());
		clone.prices = this.prices.map((price) => price.clone());
		clone.stockUid = this.stockUid;
		clone.unitUid = this.unitUid;
		clone.type = this.type;
		clone.tangible = this.tangible;
		clone.templateTickets = this.templateTickets;
		clone.onSaleEcommerceStartDatetime = this.onSaleEcommerceStartDatetime;
		clone.onSaleEcommerceEndDatetime = this.onSaleEcommerceEndDatetime;
		clone.billingAccountCodeUid = this.billingAccountCodeUid;
		return clone;
	}

	toApiIn() {
		if(this.ean13.length > 0 && this.ean13.length !== 13) throw new ProductDataError('invalid_ean13', 'Un ean13 doit faire 13 caractères de long')

		return new ProductRevisionApiIn({
			name: this.name,
			description: this.description,
			ean13: this.ean13 || null,
			uniquable: this.uniquable,
			prices: this.prices.map((price) => price.toApi()),
			groups: this.groups.map((group) => group.toApiIn()),
			mainImageUploadUid: this.mainImageUploadUid,
			tangible: this.tangible,
			stockUid: this.stockUid,
			unitUid: this.unitUid,
			templateTickets: this.templateTickets,
			type: this.type,
			onSaleEcommerceStartDatetime: this.onSaleEcommerceStartDatetime,
			onSaleEcommerceEndDatetime: this.onSaleEcommerceEndDatetime,
			billingAccountCodeUid: this.billingAccountCodeUid,
		});
	}
}