image: docker:stable

variables:
    DOCKER_BUILDKIT: 1

services:
    - docker:dind

stages:
    - build

default:
    before_script:
        - CI_COMMIT_BRANCH_DOCKER=${CI_COMMIT_BRANCH//\//\-}
        - export CI_COMMIT_BRANCH_DOCKER
        - echo "${IMAGE_NAME_FINAL}:${CI_COMMIT_BRANCH_DOCKER}"
        - echo "//gitlab.com/api/v4/packages/npm/:_authToken=${GROUPK_PACKAGE_REGISTRY_TOKEN}" >> .npmrc
        - apk add jq

cas:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/cas/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh cas

cashless-front:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/cashless-front/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh cashless-front

cashless-partner:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/cashless-partner/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh cashless-partner

littl-front:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/littl-front/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh littl-front

mastodon-front:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/mastodon-front/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh mastodon-front

point-of-sell:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH =~ /^hotfix/
          changes:
              - src/fronts/point-of-sell/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh point-of-sell

product-front:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/product-front/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh product-front

ticket-delegate-front:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/ticket-delegate-front/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh ticket-delegate-front

ticket-ecommerce:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/ticket-ecommerce/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh ticket-ecommerce

ticketing-partner:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH =~ /^hotfix/
          changes:
              - src/fronts/ticketing-partner/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh ticketing-partner

ticketing-simplified-partner:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/ticketing-simplified-partner/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh ticketing-simplified-partner

widget-front:
    stage: build
    interruptible: true
    needs: []
    rules:
        - if: $CI_COMMIT_BRANCH == "master"
          changes:
              - src/fronts/widget-front/project.json
    script:
        - chmod +x docker/gitlabci-build-images.sh && ./docker/gitlabci-build-images.sh widget-front