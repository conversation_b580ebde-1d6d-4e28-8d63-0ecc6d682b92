import {Component, Vue, Watch} from "vue-facing-decorator";
import {
	ContentHeaderComponent,
	ContentHeaderParameters, DropdownComponent,
	LayoutContentWithRightPanelComponent, ToggleComponent,
	ForbiddenMessageComponent,
} from "@groupk/vue3-interface-sdk";
import {OptionBuilder} from "vue-facing-decorator/dist/optionBuilder";
import {AutoWired, ScopedUuid, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {
	EstablishmentAccountApiOut,
	uuidScopeEstablishment,
	UuidScopeEstablishment, UuidScopeEstablishmentAccount,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	MastodonEstablishmentAccountContractAggregate,
	ProductHttpPosProfileContract
} from "@groupk/mastodon-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {Router} from "@groupk/horizon2-front";
import {EstablishmentAccountRepository} from "../../../../../shared/repositories/EstablishmentAccountRepository";
import {PosProfileApiIn, PosProfileApiOut} from "@groupk/mastodon-core";
import {PosProfileRepository} from "../../../../../shared/repositories/PosProfileRepository";
import EstablishmentAccountFormComponent
	from "../../components/EstablishmentAccountFormComponent/EstablishmentAccountFormComponent.vue";
import {AccountRepository} from "../../../../../shared/repositories/AccountRepository";
import AccountSettingsComponent
	from "../../components/SettingsComponents/AccountSettingsComponent/AccountSettingsComponent.vue";
import KycSettingsComponent from "../../components/SettingsComponents/KycSettingsComponent/KycSettingsComponent.vue";
import {PaymentProviderRepository} from "../../../../../shared/repositories/PaymentProviderRepository";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";
import WebPaymentSettingsComponent
	from "../../components/SettingsComponents/WebPaymentSettingsComponent/WebPaymentSettingsComponent.vue";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import {AppState} from "../../../../../shared/AppState";
import {AuthStateModel} from "../../../../../shared/AuthStateModel";
import EmailSettingsComponent from "../../components/SettingsComponents/EmailSettingsComponent/EmailSettingsComponent.vue";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import SettingsAccountsComponent from "../../../../../shared/components/SettingsAccountsComponent/SettingsAccountsComponent.vue";
import {ProductAppId, ProductPermissions} from "@groupk/mastodon-core";
import {
	ProductFrontAppId,
	ProductFrontPermissions, ProductFrontPermissionsTranslations
} from "../../../../../shared/mastodonCoreFront/product/ProductFrontPermissions";

@Component({
	components: {
		'layout': LayoutContentWithRightPanelComponent,
		'content-header': ContentHeaderComponent,
		'establishment-account-form': EstablishmentAccountFormComponent,
		'account-settings': AccountSettingsComponent,
		'web-payment-settings': WebPaymentSettingsComponent,
		'email-settings': EmailSettingsComponent,
		'kyc-settings': KycSettingsComponent,
		'toggle': ToggleComponent,
		'dropdown': DropdownComponent,
		'toast-manager': ToastManagerComponent,
		'forbidden-message': ForbiddenMessageComponent,
		'accounts-settings': SettingsAccountsComponent,
	}
})
export default class SettingsView extends Vue {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;

	establishmentAccounts: EstablishmentAccountApiOut[] = [];
	posProfiles: PosProfileApiOut[] = [];
	mainPosProfile: PosProfileApiOut|null = null;

	connectedEstablishmentAccountUid!: VisualScopedUuid<UuidScopeEstablishmentAccount>;

	currentPage: 'MY_ACCOUNT'|'ACCOUNTS'|'EMAILS'|'LEGAL'|'WEB_PAYMENT'|null = null;

	headerParameters: ContentHeaderParameters = {
		header: 'Paramètres',
		subtitle: 'Modifiez les paramètres de votre plateforme',
		actions: [],
		searchPlaceholder: 'Rechercher un produit',
		hideSearch: true
	}

	roles: {id: string|null, title: string, description: string}[] = [{
		id: null,
		title: 'Aucun',
		description: 'Aucun accès, ni en lecture ni en écriture'
	}, {
		id: 'accounting',
		title: 'Comptable',
		description: 'Donne accès à toutes les données en lecture mais bloque toute modification ou ajout.'
	}, {
		id: 'seller',
		title: 'Vendeur',
		description: 'Permet de gérer les commandes, travailler en caisse et gérer les profils des points de vente'
	}, {
		id: 'manager',
		title: 'Manager',
		description: 'Donne un accès complet aux données avec possibilité de lecture et modification'
	}];

	ProductAppId = ProductAppId;
	ProductFrontAppId = ProductFrontAppId;
	ProductPermissions = ProductPermissions;
	ProductFrontPermissions = ProductFrontPermissions;
	permissionTranslations = ProductFrontPermissionsTranslations;

	loading: boolean = false;
	forbidden: boolean = false;
	profileError: string|null = null;

	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(EstablishmentAccountRepository) accessor establishmentAccountRepository!: EstablishmentAccountRepository;
	@AutoWired(AccountRepository) accessor accountRepository!: AccountRepository;
	@AutoWired(PaymentProviderRepository) accessor paymentProviderRepository!: PaymentProviderRepository;
	@AutoWired(PosProfileRepository) accessor posProfileRepository!: PosProfileRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(AuthStateModel) accessor authStateModel!: AuthStateModel;

	constructor(optionBuilder: OptionBuilder, vueInstance: any) {
		super(optionBuilder, vueInstance);

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(true);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		const token = this.authStateModel.getStateSync();
		if(token && 'establishmentAccountUid' in token) {
			this.connectedEstablishmentAccountUid = token.establishmentAccountUid;
		} else if(token && 'establishmentAccounts' in token) {
			const establishmentAccount = token.establishmentAccounts.find((establishmentAccount) => establishmentAccount.establishmentUid === this.establishmentUid) ?? null;
			if(!establishmentAccount) throw new Error('user_have_no_establishment_account');
			this.connectedEstablishmentAccountUid = establishmentAccount.establishmentAccountUid;
		}

		let params = new URLSearchParams(document.location.search);
		if(params.has('page')) {
			this.currentPage = params.get('page') as 'MY_ACCOUNT'|'ACCOUNTS'|'LEGAL'|null;
		}

		this.loading = true;
	}

	@Watch('currentPage')
	currentPageWatch() {
		window.history.pushState('Settings', '', EstablishmentUrlBuilder.buildUrl('/settings?page=' + this.currentPage));
	}

	async getMainPosProfile() {
		for(let profile of this.posProfiles) {
			if(profile.iotDeviceUid === null && profile.establishmentAccountUid === null) {
				this.mainPosProfile = profile;
			}
		}

		if(!this.mainPosProfile) {
			// No main profile found so create one
			const mainProfile = new PosProfileApiIn({
				iotDeviceUid: null,
				establishmentAccountUid: null,
				displayProductsSearch: false,
				color: '#2B27DE',
				allowPurchaseDiscounts: true,
				displayProductImage: false,
				displayQuickPay: true,
				paymentMethodConfigs: [],
				performanceMode: false,
				securityCode: null,
				enableWorkClock:null
			});

			const result = await this.posProfileRepository.callContract('create', {establishmentUid: this.establishmentUid}, mainProfile);
			if(!result.isSuccess()) {
				this.profileError = 'Impossible de configurer la caisse'
			} else {
				this.mainPosProfile = result.success();
				this.posProfiles.push(this.mainPosProfile);
			}
		}
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				MastodonEstablishmentAccountContractAggregate.list,
				ProductHttpPosProfileContract.list,
			])
		})) {
			this.forbidden = true;
			this.loading = false;
			return;
		}

		this.establishmentAccounts = (await this.establishmentAccountRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();
		this.posProfiles = (await this.posProfileRepository.callContract('list', {establishmentUid: this.establishmentUid}, undefined)).success();

		await this.getMainPosProfile();
		this.loading = false;
	}
}