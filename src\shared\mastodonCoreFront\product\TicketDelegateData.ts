import {
	PublicTemplateTicketApiOut,
	TemplateTicketApiOut,
	TemplateTicketDelegateApiOut,
	TicketDesignApiOut, TicketDesignExposedAttributeApi_type,
	UuidScopeProduct_templateTicket
} from "@groupk/mastodon-core";
import {VisualScopedUuid} from "@groupk/horizon2-core";

export class TicketDelegateData {
	templateTicketUid: VisualScopedUuid<UuidScopeProduct_templateTicket>;
	publicTemplateTicket: PublicTemplateTicketApiOut;
	maxTicketGenerationCount: number|null;
	generatedTicketCount: number|null;
	allowTemplateTicketForm: boolean;
	allowTicketBatch: boolean;

	constructor(templateTicket: TemplateTicketDelegateApiOut|TemplateTicketApiOut, ticketDesigns: TicketDesignApiOut[] = []) {
		if(templateTicket instanceof TemplateTicketDelegateApiOut) {
			this.templateTicketUid = templateTicket.templateTicketUid;
			this.publicTemplateTicket = templateTicket.publicTemplateTicket;
			this.maxTicketGenerationCount = templateTicket.maxTicketGenerationCount;
			this.generatedTicketCount = templateTicket.generatedTicketCount;
			this.allowTemplateTicketForm = templateTicket.allowTemplateTicketForm;
			this.allowTicketBatch = templateTicket.allowTicketBatch;
		} else {
			let variables: TicketDesignExposedAttributeApi_type[] = [];
			for(let ticketDesign of ticketDesigns) {
				if(ticketDesign.uid === templateTicket.ticketDesignWeb) {
					variables = variables.concat(ticketDesign.exposedAttributes);
					variables = variables.concat(ticketDesign.ticketDesignTemplate.exposedAttributes);
				}
			}

			this.templateTicketUid = templateTicket.uid;
			this.publicTemplateTicket = new PublicTemplateTicketApiOut({
				uid: templateTicket.uid,
				eventUid: templateTicket.eventUid,
				name: templateTicket.name,
				type: templateTicket.type,
				variables: variables,
				faceValue: templateTicket.faceValue ?? null,
				stockUidList: []
			});
			this.maxTicketGenerationCount = null;
			this.generatedTicketCount = null;
			this.allowTemplateTicketForm = true;
			this.allowTicketBatch = true;
		}
	}
}