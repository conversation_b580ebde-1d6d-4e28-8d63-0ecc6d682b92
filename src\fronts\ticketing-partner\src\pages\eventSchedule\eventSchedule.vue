<script lang="ts" src="./eventSchedule.ts">
</script>

<style lang="sass">
@use './eventSchedule.scss' as *
</style>

<template>
    <div id="event-schedule-page">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>
        <div class="no-temporal-stock" v-else-if="temporalStocks.length === 0">
            <div class="title"> Aucun calendrier </div>
            <div class="subtitle"> Créez un calendrier pour commencer </div>
            <button class="small white button" @click="showStockModal = true">
                <i class="fa-regular fa-circle-plus"></i>
                Créer un calendrier
            </button>
        </div>
        <template v-else>
            <div class="page-header">
                <div class="left">
                    <div class="headline">
                        <h1> Disponibilités - {{ event.name }} </h1>
                        <span> G<PERSON>rez les réservations de votre événement </span>
                    </div>

                    <div class="actions">
                        <div class="navigation">
                            <div class="small white button" @click="previousWeek()">
                                <i class="fa-regular fa-arrow-left" ></i>
                            </div>
                            <span class="current-month" v-if="currentMonday && currentSunday">
                                {{ $filters.DayNumber(currentMonday.toISOString()) }}
                                - {{ $filters.DayNumber(currentSunday.toISOString()) }}
                                {{ $filters.Month(currentSunday.toISOString()) }}
                                {{ $filters.Year(currentSunday.toISOString()) }}
                            </span>
                            <div class="small white button" @click="nextWeek()">
                                <i class="fa-regular fa-arrow-right"></i>
                            </div>
                        </div>

                        <div class="small white button" @click="currentWeek()">
                            Aujourd'hui
                        </div>

                        <div class="small white button" @click="showTicketImportModal = true;">
                            <i class="fa-regular fa-arrow-down-to-line"></i>
                            Importer
                        </div>

                        <div class="small white button" @click="showExportModal = true;">
                            <i class="fa-regular fa-arrow-up-from-line"></i>
                            Exporter
                        </div>
                    </div>
                </div>
                <div class="right">
                    <div class="top">
                        <div class="big input-group">
                            <dropdown
                                :default-selected="currentStockTemporal.uid"
                                :values="getTemporalStockDropdownValues()"
                                @update="setCurrentStockTemporal(requireStockTemporalWithUid($event))"
                            ></dropdown>
                        </div>

                        <button class="grey button" @click="editingStockTemporal = currentStockTemporal; showStockModal = true">
                            <i class="fa-regular fa-pen-line"></i>
                        </button>

                        <button class="grey button" @click="showStockModal = true">
                            <i class="fa-regular fa-plus"></i>
                        </button>
                    </div>

                    <div class="zoom">
                        <button class="tertiary button" @click="zoomOut()">
                            <i class="fa-regular fa-magnifying-glass-minus"></i>
                            Zoom -
                        </button>
                        <button class="tertiary button" @click="zoomIn()">
                            <i class="fa-regular fa-magnifying-glass-plus"></i>
                            Zoom +
                        </button>
                    </div>

                </div>
            </div>

            <full-width-calendar
                v-if="currentStockTemporal"
                :key="currentStockTemporal.uid"
                :stock-temporal="currentStockTemporal"
                :events="events"
                @changed-week="currentMonday = $event; computeEventsForWeek($event)"
                @clicked-event="showTemporalData = $event.startDate"
                @updated="updatedStockTemporal($event)"
                ref="calendar"
            ></full-width-calendar>

            <div class="side-content" v-if="showTemporalData">
                <div class="close" @click="showTemporalData = null">
                    <i class="fa-regular fa-xmark"></i>
                    <span> Fermer </span>
                </div>

                <div class="header">
                    <div class="left">
                        <h2> Billets du {{ $filters.Date(showTemporalData.toISOString()) }} {{ $filters.Hour(showTemporalData.toISOString()) }} </h2>
                        <span>
                           Liste des billets
                        </span>
                    </div>
                </div>

                <div class="reservations">
                    <order-buyer
                        v-for="order of getOrdersOnTemporal(showTemporalData)"
                        :key="order.uid"
                        :order="order"
                        :tickets="usages.ticketList"
                        :customers="usages.customerList"
                        :template-tickets="templateTickets"
                        :metadata-descriptors="metadataDescriptors"
                        :formatted-temporal-datetime="formatDateToTemporalDateTime(showTemporalData)"
                        @orderDropdownClicked="orderDropdownClicked"
                    ></order-buyer>

                    <batch-buyer
                        v-for="batch of getBatchesOnTemporal(showTemporalData)"
                        :key="batch.uid"
                        :batch="batch"
                        :tickets="usages.ticketList"
                        :customers="usages.customerList"
                        :template-tickets="templateTickets"
                        :metadata-descriptors="metadataDescriptors"
                        @orderDropdownClicked="orderDropdownClicked"
                    ></batch-buyer>
                </div>

                <div class="toggle-canceled" @click="displayCanceledOrders = !displayCanceledOrders" v-if="getOrdersOnTemporal(showTemporalData, true).length > 0">
                    Afficher les commandes annulées
                    <i class="fa-regular fa-chevron-down" v-if="!displayCanceledOrders"></i>
                    <i class="fa-regular fa-chevron-up" v-else></i>
                </div>

                <div class="reservations" v-if="displayCanceledOrders">
                    <order-buyer
                        v-for="order of getOrdersOnTemporal(showTemporalData, true)"
                        :key="order.uid"
                        :order="order"
                        :tickets="usages.ticketList"
                        :customers="usages.customerList"
                        :template-tickets="templateTickets"
                        :metadata-descriptors="metadataDescriptors"
                        :formatted-temporal-datetime="formatDateToTemporalDateTime(showTemporalData)"
                        @orderDropdownClicked="orderDropdownClicked"
                    ></order-buyer>

                    <batch-buyer
                        v-for="batch of getBatchesOnTemporal(showTemporalData, true)"
                        :key="batch.uid"
                        :batch="batch"
                        :tickets="usages.ticketList"
                        :customers="usages.customerList"
                        :template-tickets="templateTickets"
                        :metadata-descriptors="metadataDescriptors"
                        @orderDropdownClicked="orderDropdownClicked"
                    ></batch-buyer>
                </div>


                <button class="black button" @click="showTicketImportModal = true">
                    <i class="fa-regular fa-plus"></i>
                    Commande manuelle
                </button>
            </div>
        </template>

        <stock-temporal-form
            v-if="showStockModal"
            :editing-stock-temporal="editingStockTemporal"
            @created="createdStockTemporal($event)"
            @updated="createdStockTemporal($event)"
            @close="showStockModal = false; editingStockTemporal = null"
        ></stock-temporal-form>

        <ticket-import
            v-if="showTicketImportModal && event"
            :state="showTicketImportModal"
            :event="event"
            :temporal-slot="{stockUid: currentStockTemporal.uid, datetime: showTemporalData ? formatDateToTemporalDateTime(showTemporalData) : null}"
            @created-batch="currentMonday ? computeEventsForWeek(currentMonday) : ''"
            @close="showTicketImportModal = false"
        ></ticket-import>

        <calendar-export
            v-if="showExportModal"
            :event="event"
            :stock-temporal="currentStockTemporal"
            @close="showExportModal = false"
        ></calendar-export>
    </div>

    <form-modal-or-drawer
        :title="'Modifier ' + editingBatchMetadata?.metadataDescriptor.descriptorTemplate.name"
        :subtitle="'Modifier ' + editingBatchMetadata?.metadataDescriptor.descriptorTemplate.name + ' de cette commande'"
        :state="editingBatchMetadata"
        @close="editingBatchMetadata = null"
    >
        <template v-slot:content>
            <div class="input-group" v-if="editingBatchMetadata">
                <metadata-input
                    :metadata="editingBatchMetadata.metadata"
                    :metadata-descriptor="editingBatchMetadata.metadataDescriptor"
                ></metadata-input>

                <div class="form-error" v-if="metadataUpdateError">
                    <i class="fa-regular fa-circle-exclamation"></i>
                    <div class="details">
                        {{ metadataUpdateError }}
                    </div>
                </div>
            </div>
        </template>

        <template v-slot:buttons>
            <button class="white button" @click="editingBatchMetadata = null">
                Annuler
            </button>
            <button class="button" :class="{loading: updatingMetadata, disabled: updatingMetadata}" @click="updateBatchMetadata()">
                <i class="fa-regular fa-pen-line"></i>
                Modifier
            </button>
        </template>
    </form-modal-or-drawer>
</template>
