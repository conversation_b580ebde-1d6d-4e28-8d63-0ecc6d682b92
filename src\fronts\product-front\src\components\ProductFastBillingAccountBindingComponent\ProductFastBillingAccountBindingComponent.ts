import {Component, Vue, Watch} from "vue-facing-decorator";
import {
    DropdownComponent,
    DropdownValue,
    FormModalOrDrawerComponent,
    TablePagination, ToggleComponent
} from "@groupk/vue3-interface-sdk";
import {
    AutoWired, QueryFilterGroupClause,
    QueryOperator, ScopedUuid,
    TypedQuerySearch,
    Uuid, UuidUtils,
    VisualScopedUuid
} from "@groupk/horizon2-core";
import {ProductRepository} from "../../../../../shared/repositories/ProductRepository";
import {
    AppApiOut_type,
    ApplicationPermission, AppReservitApiOut, AppType,
    BillingAccountApiIn,
    BillingAccountApiOut,
    BillingAccountCodeApiOut,
    EstablishmentAccountPermissionModel,
    ProductApiOut,
    ProductBillingAccountHttpContract,
    ProductHttpProductContract,
    ProductRevisionNotDetailedApiOut,
    ProductRevisionSearchConfig,
    ProductType, ReservitBillingAccountCodeImportApiIn,
} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";
import {BillingAccountRepository} from "../../../../../shared/repositories/BillingAccountRepository";
import {BillingAccountUtils} from "../../../../../shared/utils/BillingAccountUtils";
import ProductImageComponent from "../ProductImageComponent/ProductImageComponent.vue";
import {UuidScopeProductProductRevision} from "@groupk/mastodon-core";
import {TypedQueryFilterGroup} from "@groupk/horizon2-core";
import BillingAccountFormComponent from "./BillingAccountFormComponent/BillingAccountFormComponent.vue";
import {UuidScopeProduct_billingAccountCode} from "@groupk/mastodon-core";
import {ReservitAppRepository} from "../../../../../shared/repositories/ReservitAppRepository";
import {AppRepository} from "../../../../../shared/repositories/AppRepository";

export function ProductFastBillingAccountBindingComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]) {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        ProductHttpProductContract.list,
        ProductHttpProductContract.searchRevision,
        ProductHttpProductContract.searchRevisionCount,
        ProductBillingAccountHttpContract.set
    ]);
}

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'product-image': ProductImageComponent,
        'dropdown': DropdownComponent,
        'toggle': ToggleComponent,
        'billing-account-form': BillingAccountFormComponent
    }
})
export default class ProductFastBillingAccountBindingComponent extends Vue {

    apps: AppApiOut_type[] = [];
    products: ProductApiOut[] = [];
    productRevisions: ProductRevisionNotDetailedApiOut[] = [];

    billingAccountCodes: BillingAccountCodeApiOut[] = [];
    billingAccounts: Map<ScopedUuid<UuidScopeProductProductRevision>, BillingAccountApiOut> = new Map();
    productBillingAccounts: Map<VisualScopedUuid<UuidScopeProductProductRevision>, ScopedUuid<UuidScopeProduct_billingAccountCode>> = new Map();

    lastKeyboardEvent: KeyboardEvent|null = null;

    missingOnly: boolean = true;

    loading: boolean = true;
    opened: boolean = false;
    saving: boolean = false;
    importingFromReservit: boolean = false;
    showPasteModal: boolean = false;
    showCustomValueModal: ProductRevisionNotDetailedApiOut|null = null;
    pastedData: string = '';
    pasteImportError: string|null = null;
    search: string = '';
    error: string|null = null;

    pagination: TablePagination = {
        totalResults: 0,
        resultsPerPage: 150,
        currentPage: 1,
        estimateTotal: false
    }

    @AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
    @AutoWired(BillingAccountRepository) accessor billingAccountRepository!: BillingAccountRepository;
    @AutoWired(ReservitAppRepository) accessor reservitAppRepository!: ReservitAppRepository;
    @AutoWired(AppRepository) accessor appRepository!: AppRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        this.apps = (await this.appRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;

        this.billingAccountCodes = (await this.billingAccountRepository.callContract('listCode', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;

        const billingAccounts = await BillingAccountUtils.getAllBillingAccounts();
        this.billingAccounts = new Map(billingAccounts.map(account => [account.targetUid, account]));

        this.products = (await this.productRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
        await this.searchProductRevisions();

        window.addEventListener('keydown', this.handleKeyDown);
        window.addEventListener('keyup', this.handleKeyUp);

        this.loading = false;
    }

    unmounted() {
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('keyup', this.handleKeyUp);
    }

    async searchProductRevisions(cursor: {
        after?: Uuid,
        before?: Uuid
    } | null = null) {
        this.loading = true;

        const filters: TypedQuerySearch<typeof ProductRevisionSearchConfig> = {};

        const filter: TypedQueryFilterGroup<(typeof ProductRevisionSearchConfig)['filters']> = {
            group: QueryFilterGroupClause.AND,
            filters: [{
                group: QueryFilterGroupClause.OR,
                filters: [{
                    name: 'type',
                    value: ProductType.FOOD_INGREDIENT,
                    operator: QueryOperator.DIFFERENT
                }, {
                    name: 'type',
                    value: null,
                    operator: QueryOperator.EQUAL
                }]
            }]
        }

        if(this.search.length > 0) {
            filter.filters.push({
                name: 'name',
                value: this.search,
                operator: QueryOperator.CONTAINS,
            });
        }

        if(this.missingOnly) {
            filter.filters.push({
                name: 'hasMissingBillingAccount',
                value: true,
                operator: QueryOperator.EQUAL,
            });
        }

        filters.filter = filter;

        if (!cursor) {
            const data = (await this.productRepository.callContract('searchRevisionCount', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, filters)).success();
            this.pagination.totalResults = data.rows;
            this.pagination.currentPage = 1;
            this.pagination.estimateTotal = data.estimate;
        }

        filters.elementsPerPage = this.pagination.resultsPerPage;
        if (cursor && cursor.after) filters.cursorAfter = cursor.after;
        if (cursor && cursor.before) filters.cursorBefore = cursor.before;
        this.productRevisions = (await this.productRepository.callContract('searchRevision', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, filters)).success();

        this.loading = false;
    }

    nextPage() {
        if(this.loading) return;
        this.pagination.currentPage++;
        this.searchProductRevisions({after: this.productRevisions[this.productRevisions.length - 1].uid})
    }

    previousPage() {
        if(this.loading) return;
        this.pagination.currentPage--;
        this.searchProductRevisions({before: this.productRevisions[0].uid})
    }

    getReservitApp(): AppReservitApiOut|null {
        return this.apps.find((app) => app.type === AppType.RESERVIT) ?? null;
    }

    async importReservitAccounts() {
        this.importingFromReservit = true;

        const reservitApp = this.getReservitApp();
        if(!reservitApp) return;
        await this.reservitAppRepository.callContract('importBillingAccountCode', {
            establishmentUid: this.appState.requireUrlEstablishmentUid(),
            appUid: reservitApp.uid
        }, new ReservitBillingAccountCodeImportApiIn({
            forceImportName: false
        }));

        this.billingAccountCodes = (await this.billingAccountRepository.callContract('listCode', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success().list;

        this.importingFromReservit = false;
    }

    private searchTimeout: ReturnType<typeof setTimeout>|null = null;

    @Watch('search')
    onSearchChange() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        this.searchTimeout = setTimeout(() => {
            this.searchProductRevisions();
        }, 500);
    }

    handleKeyDown(e: KeyboardEvent) {
        this.lastKeyboardEvent = e;
        if(e.ctrlKey && e.shiftKey && e.code === 'KeyV') {
            this.showPasteModal = true;
            e.preventDefault();
        }
    }

    handleKeyUp() {
        this.lastKeyboardEvent = null;
    }

    getRevisionBillingAccount(revision: ProductRevisionNotDetailedApiOut): string|null {
        const notSavedAccount = this.productBillingAccounts.get(revision.uid);
        if(notSavedAccount) return notSavedAccount;

        const account = this.billingAccounts.get(UuidUtils.visualToScoped<UuidScopeProductProductRevision>(revision.uid)) ?? null;
        if(account) return this.requireBillingAccountCodeWithUid(account.billingAccountCodeUid).uid;

        return null;
    }

    get billingAccountDropdownValues(): DropdownValue[] {
        return this.billingAccountCodes.map((billingAccountCode) => {
            return {
                name: billingAccountCode.name,
                value: billingAccountCode.uid
            }
        })
    }

    setProductBillingAccount(productRevisionUid: VisualScopedUuid<UuidScopeProductProductRevision>, billingAccountCodeUid: ScopedUuid<UuidScopeProduct_billingAccountCode>) {
        if (billingAccountCodeUid) {
            this.productBillingAccounts.set(productRevisionUid, billingAccountCodeUid);
        } else {
            this.productBillingAccounts.delete(productRevisionUid);
        }
    }

    onBillingAccountCreated(billingAccountCode: BillingAccountCodeApiOut) {
        this.billingAccountCodes.push(billingAccountCode);
        if (this.showCustomValueModal) {
            this.setProductBillingAccount(this.showCustomValueModal.uid, billingAccountCode.uid);
            this.showCustomValueModal = null;
        }
    }

    onBillingAccountFormClose() {
        this.showCustomValueModal = null;
    }

    async save() {
        this.saving = true;

        const tasks = Array.from(this.productBillingAccounts.entries()).map(([productRevisionUid, account]) =>
            this.billingAccountRepository.callContract('set', {
                establishmentUid: this.appState.requireUrlEstablishmentUid(),
                targetUid: productRevisionUid as VisualScopedUuid<UuidScopeProductProductRevision>,
            }, new BillingAccountApiIn({
                billingAccountCodeUid: account
            }))
        );
        await Promise.all(tasks);

        this.saving = false;
        this.close();
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }

    processPastedData() {
        // Reset error state
        this.pasteImportError = null;

        if (!this.pastedData.trim()) {
            this.pasteImportError = "Aucune donnée n'a été collée";
            return;
        }

        try {
            // Split the pasted data into lines
            const lines = this.pastedData.trim().split('\n');
            let errorProducts: string[] = [];
            let errorBillingAccounts: string[] = [];

            for (const line of lines) {
                // Skip empty lines
                if (!line.trim()) continue;

                // Parse the line - expecting format: ProductName, BillingAccountName
                const match = line.match(/([^,]+),\s*(.+)/);
                if (!match) {
                    this.pasteImportError = `Format incorrect: ${line}. Format attendu: Nom produit, Nom compte comptable`;
                    return;
                }

                const [, productName, billingAccountName] = match;

                // Find the product by name
                const productRevision = this.productRevisions.find(pr =>
                    pr.name.toLowerCase() === productName.trim().toLowerCase()
                );

                if (!productRevision) {
                    errorProducts.push(productName);
                    continue;
                }

                const billingAccountCode = this.billingAccountCodes.find((code) => code.name.toLowerCase() === billingAccountName.trim().toLowerCase());
                if (!billingAccountCode) {
                    errorBillingAccounts.push(billingAccountName);
                    continue;
                }

                // Set the billing account for the product
                this.setProductBillingAccount(productRevision.uid, billingAccountCode.uid);
            }

            // If there were products not found, show an error
            if (errorProducts.length > 0 || errorBillingAccounts.length > 0) {
                if(errorProducts.length > 0) {
                    this.pasteImportError = `Produits non trouvés: ${errorProducts.join(', ')} `;
                }
                if (errorBillingAccounts.length > 0) {
                    this.pasteImportError += `Comptes comptables non trouvés: ${errorBillingAccounts.join(', ')}`;
                }
            } else {
                // Close the paste modal if everything was successful
                this.$forceUpdate();
                this.pastedData = '';
                this.showPasteModal = false;
            }
        } catch (error) {
            this.pasteImportError = `Erreur lors du traitement des données: ${error}`;
        }
    }

    private productMap: Map<string, ProductApiOut> = new Map();

    getProductWithRevision(revision: ProductRevisionNotDetailedApiOut) {
        if (!this.productMap.size) {
            this.products.forEach(product => {
                this.productMap.set(product.uid, product);
            });
        }
        return this.productMap.get(revision.productUid) ?? null;
    }

    requireBillingAccountCodeWithUid(billingAccountCodeUid: ScopedUuid<UuidScopeProduct_billingAccountCode>) {
        const billingAccountCode = this.billingAccountCodes.find((code) => code.uid === billingAccountCodeUid);
        if (!billingAccountCode) throw new Error('missing_billing_account_code');
        return billingAccountCode;
    }
}
