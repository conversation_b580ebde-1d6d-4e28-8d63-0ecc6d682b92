{"name": "merged-fronts", "version": "1.0.38", "scripts": {"test": "vitest run", "builddev-cas": "cd src/fronts/cas && vite --config vite.config.ts --port 9700 --host 0.0.0.0 --force", "builddev-cashless-front": "cd src/fronts/cashless-front && vite --config vite.config.ts --port 9366 --host 0.0.0.0 --force", "builddev-cashless-partner": "cd src/fronts/cashless-partner && vite --config vite.config.ts --port 9400 --host 0.0.0.0 --force", "builddev-point-of-sell": "cd src/fronts/point-of-sell && vite --config vite.config.ts --port 9600 --host 0.0.0.0 --force", "builddev-product-front": "cd src/fronts/product-front && vite --config vite.config.ts --port 9201 --host 0.0.0.0 --force", "builddev-ticket-ecommerce": "cd src/fronts/ticket-ecommerce && vite --config vite.config.ts --port 9355 --host 0.0.0.0 --force", "builddev-ticketing-partner": "cd src/fronts/ticketing-partner && vite --config vite.config.ts --port 9857 --host 0.0.0.0 --force", "builddev-ticket-delegate-front": "cd src/fronts/ticket-delegate-front && vite --config vite.config.ts --port 9222 --host 0.0.0.0 --force", "builddev-ticketing-simplified-partner": "cd src/fronts/ticketing-simplified-partner && vite --config vite.config.ts --port 9358 --host 0.0.0.0 --force", "builddev-mastodon-front": "cd src/fronts/mastodon-front && vite --config vite.config.ts --port 9888 --host 0.0.0.0 --force", "builddev-littl-front": "cd src/fronts/littl-front && vite --config vite.config.ts --port 9555 --host 0.0.0.0 --force", "builddev-widget-front": "cd src/fronts/widget-front && tsc --watch", "buildprod-base": "cd src/fronts/ && npx shx mkdir -p ./$FRONT_NAME/dist/ && npx shx cp -r ./base/* ./$FRONT_NAME/dist && npx shx cp -r ./base/.well-known ./$FRONT_NAME/dist", "buildprod-cas": "cd src/fronts/cas && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-cashless-front": "cd src/fronts/cashless-front && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-cashless-partner": "cd src/fronts/cashless-partner && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-point-of-sell": "cd src/fronts/point-of-sell && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-product-front": "cd src/fronts/product-front && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-ticket-ecommerce": "cd src/fronts/ticket-ecommerce && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-ticketing-partner": "cd src/fronts/ticketing-partner && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-ticket-delegate-front": "cd src/fronts/ticket-delegate-front && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-ticketing-simplified-partner": "cd src/fronts/ticketing-simplified-partner && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-mastodon-front": "cd src/fronts/mastodon-front && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-littl-front": "cd src/fronts/littl-front && vue-tsc --noEmit && vite build --emptyOutDir=false", "buildprod-widget-front": "cd src/fronts/widget-front && tsc && npx shx cp -r ./src/public/* ./dist", "buildprod-all": "npm run buildprod-cas && npm run buildprod-cashless-front && npm run buildprod-cashless-partner && npm run buildprod-point-of-sell && npm run buildprod-product-front && npm run buildprod-ticket-ecommerce && npm run buildprod-ticketing-partner && npm run buildprod-ticket-delegate-front && npm run buildprod-ticketing-simplified-partner && npm run buildprod-mastodon-front && npm run buildprod-littl-front", "preview-point-of-sell": "cd src/fronts/point-of-sell && vite preview --config vite.config.ts --port 9600 --host 0.0.0.0", "typecheck-all": "cd src/fronts/cas && vue-tsc --noEmit && cd ../cashless-front && vue-tsc --noEmit && cd ../cashless-partner && vue-tsc --noEmit && cd ../point-of-sell && vue-tsc --noEmit && cd ../product-front && vue-tsc --noEmit && cd ../ticket-ecommerce && vue-tsc --noEmit && cd ../ticketing-partner && vue-tsc --noEmit && cd ../ticket-delegate-front && vue-tsc --noEmit && cd ../ticketing-simplified-partner && vue-tsc --noEmit && cd ../mastodon-front && vue-tsc --noEmit && cd ../littl-front && vue-tsc --noEmit"}, "author": "", "private": true, "dependencies": {"@groupk/caisse-protocol": "1.0.8", "@groupk/cashkeeper-protocol": "1.0.9", "@groupk/escpos-protocol": "1.0.1", "@groupk/font-awesome-sdk": "1.0.0", "@groupk/horizon2-back": "2.0.102", "@groupk/horizon2-core": "2.0.102", "@groupk/horizon2-front": "2.0.102", "@groupk/ipp-protocol": "1.0.1", "@groupk/mastodon-core": "2.0.79", "@groupk/native-bridge": "2.1.10", "@groupk/vue3-interface-sdk": "3.2.45-136", "@playwright/test": "^1.53.1", "@sentry/browser": "^7.83.0", "@shopify/draggable": "^1.1.4", "@stripe/stripe-js": "^7.0.0", "@zxing/library": "^0.21.3", "chart.js": "^4.4.1", "cleave.js": "^1.6.0", "papaparse": "^5.4.1", "qrcode": "^1.5.4", "vue": "^3.5.13", "vue-facing-decorator": "^3.0.4", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@groupk/gversion": "1.0.25", "@groupk/vite-vfc-intellij": "1.0.2", "@types/cleave.js": "^1.4.12", "@types/google.maps": "^3.55.9", "@types/node": "^18.15.5", "@types/papaparse": "^5.3.14", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.0", "@vitejs/plugin-vue": "^5.0.0", "@vitest/ui": "^3.2.4", "canvas": "^3.1.2", "cross-env": "^7.0.3", "jsdom": "^26.1.0", "sass": "1.55.0", "shx": "^0.4.0", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "^6.2.5", "vite-tsconfig-paths": "^4.3.2", "vitest": "^3.2.4", "vue-tsc": "^2.1.10"}}