.product-ticket-component {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 20px;
    border-radius: 12px;
    background: #F2F2F2;

    .quick-data {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        gap: 10px;

        .left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;
        }

        .data {
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: 600;
        }

        .label {
            background: #dadada;
            color: black;
            border: none;
            border-radius: 50px;

            &.orange {
                color: rgba(0, 0, 0, 0.8);
                background: rgba(255, 140, 0, 0.2);
            }
        }

        .trending {
            color: #ff1f1f;
        }
    }


    .name {
        font-size: 15px;
        font-weight: 500;
    }

    .description {
        color: #494949;
        font-size: 14px;
    }

    .bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;

        .price {
            font-size: 16px;
        }

        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 10px;

            .quantity {
                font-size: 16px;

                &.highlighted {
                    font-weight: bold;
                }
            }

            .minus, .plus {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #2f2f2f;
                cursor: pointer;

                &:hover {
                    background: black;
                }

                &.disabled {
                    pointer-events: none;
                    opacity: 0.6;
                }

                i {
                    font-size: 12px;
                    color: white;
                }
            }

            .minus {
                background: lightgrey;
                &:hover {
                    background: #afafaf;
                }
            }
        }
    }
}